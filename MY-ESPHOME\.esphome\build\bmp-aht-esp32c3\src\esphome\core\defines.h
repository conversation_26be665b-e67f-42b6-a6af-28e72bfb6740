#pragma once
#include "esphome/core/macros.h"
#define ESPHOME_BOARD "airm2m_core_esp32c3"
#define ESPHOME_VARIANT "ESP32-C3"
#define USE_API
#define USE_API_PLAINTEXT
#define USE_ARDUINO_VERSION_CODE VERSION_CODE(3, 1, 3)
#define USE_BINARY_SENSOR
#define USE_CLIMATE
#define USE_ESPHOME_TASK_LOG_BUFFER
#define USE_I2C
#define USE_LOGGER
#define USE_LOGGER_USB_CDC
#define USE_MD5
#define USE_MDNS
#define USE_NETWORK
#define USE_NETWORK_IPV6 false
#define USE_OTA
#define USE_OTA_VERSION 2
#define USE_SENSOR
#define USE_SOCKET_IMPL_BSD_SOCKETS
#define USE_SOCKET_SELECT_SUPPORT
#define USE_WIFI
