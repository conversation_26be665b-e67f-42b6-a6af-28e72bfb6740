esphome:
  name: bmp_aht_esp32c3
  friendly_name: 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
logger:

api:

ota:
  platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

remote_transmitter:
  pin: GPIO19
  carrier_duty_percent: 50%
  id: my_transmitter

i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT20 Temperature and Humidity Sensor (Fixed address 0x38)
  - platform: aht10
    temperature:
      name: "AHT20 Temperature"
    humidity:
      name: "AHT20 Humidity"
    update_interval: 60s

  # BMP280 Pressure Sensor
  - platform: bmp280_i2c
    temperature:
      name: "BMP280 Temperature"
    pressure:
      name: "BMP280 Pressure"
    address: 0x77
    i2c_id: bus_a
    update_interval: 60s

climate:
  - platform: gree
    name: Gree Air Conditioner
    model: yac1fb9
    transmitter_id: my_transmitter

