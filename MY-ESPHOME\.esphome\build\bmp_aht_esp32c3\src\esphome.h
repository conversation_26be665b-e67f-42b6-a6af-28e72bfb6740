#pragma once
#include "esphome/core/macros.h"
#include "esphome\components\aht10\aht10.h"
#include "esphome\components\api\api_connection.h"
#include "esphome\components\api\api_frame_helper.h"
#include "esphome\components\api\api_noise_context.h"
#include "esphome\components\api\api_pb2.h"
#include "esphome\components\api\api_pb2_service.h"
#include "esphome\components\api\api_server.h"
#include "esphome\components\api\custom_api_device.h"
#include "esphome\components\api\homeassistant_service.h"
#include "esphome\components\api\list_entities.h"
#include "esphome\components\api\proto.h"
#include "esphome\components\api\subscribe_state.h"
#include "esphome\components\api\user_services.h"
#include "esphome\components\binary_sensor\automation.h"
#include "esphome\components\binary_sensor\binary_sensor.h"
#include "esphome\components\binary_sensor\filter.h"
#include "esphome\components\bmp280_base\bmp280_base.h"
#include "esphome\components\bmp280_i2c\bmp280_i2c.h"
#include "esphome\components\climate\automation.h"
#include "esphome\components\climate\climate.h"
#include "esphome\components\climate\climate_mode.h"
#include "esphome\components\climate\climate_traits.h"
#include "esphome\components\climate_ir\climate_ir.h"
#include "esphome\components\esp32\gpio.h"
#include "esphome\components\esp32\preferences.h"
#include "esphome\components\esphome\ota\ota_esphome.h"
#include "esphome\components\gree\gree.h"
#include "esphome\components\i2c\i2c.h"
#include "esphome\components\i2c\i2c_bus.h"
#include "esphome\components\i2c\i2c_bus_arduino.h"
#include "esphome\components\i2c\i2c_bus_esp_idf.h"
#include "esphome\components\logger\logger.h"
#include "esphome\components\logger\task_log_buffer.h"
#include "esphome\components\md5\md5.h"
#include "esphome\components\mdns\mdns_component.h"
#include "esphome\components\network\ip_address.h"
#include "esphome\components\network\util.h"
#include "esphome\components\ota\automation.h"
#include "esphome\components\ota\ota_backend.h"
#include "esphome\components\ota\ota_backend_arduino_esp32.h"
#include "esphome\components\ota\ota_backend_arduino_esp8266.h"
#include "esphome\components\ota\ota_backend_arduino_libretiny.h"
#include "esphome\components\ota\ota_backend_arduino_rp2040.h"
#include "esphome\components\ota\ota_backend_esp_idf.h"
#include "esphome\components\preferences\syncer.h"
#include "esphome\components\remote_base\abbwelcome_protocol.h"
#include "esphome\components\remote_base\aeha_protocol.h"
#include "esphome\components\remote_base\beo4_protocol.h"
#include "esphome\components\remote_base\byronsx_protocol.h"
#include "esphome\components\remote_base\canalsat_protocol.h"
#include "esphome\components\remote_base\coolix_protocol.h"
#include "esphome\components\remote_base\dish_protocol.h"
#include "esphome\components\remote_base\dooya_protocol.h"
#include "esphome\components\remote_base\drayton_protocol.h"
#include "esphome\components\remote_base\gobox_protocol.h"
#include "esphome\components\remote_base\haier_protocol.h"
#include "esphome\components\remote_base\jvc_protocol.h"
#include "esphome\components\remote_base\keeloq_protocol.h"
#include "esphome\components\remote_base\lg_protocol.h"
#include "esphome\components\remote_base\magiquest_protocol.h"
#include "esphome\components\remote_base\midea_protocol.h"
#include "esphome\components\remote_base\mirage_protocol.h"
#include "esphome\components\remote_base\nec_protocol.h"
#include "esphome\components\remote_base\nexa_protocol.h"
#include "esphome\components\remote_base\panasonic_protocol.h"
#include "esphome\components\remote_base\pioneer_protocol.h"
#include "esphome\components\remote_base\pronto_protocol.h"
#include "esphome\components\remote_base\raw_protocol.h"
#include "esphome\components\remote_base\rc5_protocol.h"
#include "esphome\components\remote_base\rc6_protocol.h"
#include "esphome\components\remote_base\rc_switch_protocol.h"
#include "esphome\components\remote_base\remote_base.h"
#include "esphome\components\remote_base\roomba_protocol.h"
#include "esphome\components\remote_base\samsung36_protocol.h"
#include "esphome\components\remote_base\samsung_protocol.h"
#include "esphome\components\remote_base\sony_protocol.h"
#include "esphome\components\remote_base\toshiba_ac_protocol.h"
#include "esphome\components\remote_base\toto_protocol.h"
#include "esphome\components\remote_transmitter\remote_transmitter.h"
#include "esphome\components\safe_mode\automation.h"
#include "esphome\components\safe_mode\safe_mode.h"
#include "esphome\components\sensor\automation.h"
#include "esphome\components\sensor\filter.h"
#include "esphome\components\sensor\sensor.h"
#include "esphome\components\socket\headers.h"
#include "esphome\components\socket\socket.h"
#include "esphome\components\wifi\wifi_component.h"
#include "esphome\core\application.h"
#include "esphome\core\area.h"
#include "esphome\core\automation.h"
#include "esphome\core\base_automation.h"
#include "esphome\core\color.h"
#include "esphome\core\component.h"
#include "esphome\core\component_iterator.h"
#include "esphome\core\controller.h"
#include "esphome\core\datatypes.h"
#include "esphome\core\defines.h"
#include "esphome\core\device.h"
#include "esphome\core\doxygen.h"
#include "esphome\core\entity_base.h"
#include "esphome\core\event_pool.h"
#include "esphome\core\gpio.h"
#include "esphome\core\hal.h"
#include "esphome\core\helpers.h"
#include "esphome\core\lock_free_queue.h"
#include "esphome\core\log.h"
#include "esphome\core\log_const_en.h"
#include "esphome\core\macros.h"
#include "esphome\core\optional.h"
#include "esphome\core\preferences.h"
#include "esphome\core\ring_buffer.h"
#include "esphome\core\scheduler.h"
#include "esphome\core\string_ref.h"
#include "esphome\core\time.h"
#include "esphome\core\util.h"
#include "esphome\core\version.h"

